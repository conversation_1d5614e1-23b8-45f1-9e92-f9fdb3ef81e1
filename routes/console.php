<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

Artisan::command('queue:work-whatsapp', function () {
    $this->info('🚀 Starting WhatsApp Queue Worker...');
    $this->info('📋 Processing jobs from: whatsapp-ai, default queues');

    // Run the queue worker with specific configuration
    $this->call('queue:work', [
        '--queue' => 'whatsapp-ai,default',
        '--verbose' => true,
        '--tries' => 3,
        '--timeout' => 90,
        '--sleep' => 3,
        '--max-jobs' => 1000,
        '--max-time' => 3600, // 1 hour
    ]);
})->purpose('Start WhatsApp queue worker for processing AI responses and messages');
