<?php

namespace App\Services\SocialMediaServices;

use App\Data\WhatsApp\Btns;
use App\Data\WhatsApp\WhatsappBtns;
use App\Models\WhatsApp\{WhatsAppProfile, WhatsAppMessage, WhatsAppSettings};
use App\Services\AIServices\ChatService;
use App\Services\SocialMediaServices\WhatsAppCacheService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class WhatsappService
{

    private static function makeHttpRequest($endpoint, $data = null, $method = 'POST')
    {
        try {
            $serverUrl = config('services.whatsapp_server');
            $url = $serverUrl . $endpoint;

            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])->send($method, $url, [
                'json' => $data,
            ]);

            if ($response->failed()) {
                throw new \Exception($response->body());
            }

            return $response->json();
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private static function sendTextMessage($data)
    {
        return self::makeHttpRequest('/send-message', $data);
        try {
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    private static function sendMediaMessage($data)
    {
        return self::makeHttpRequest('/send-message', $data);
    }

    private static function sendLocationMessage($data)
    {
        return self::makeHttpRequest('/send-message', $data);
    }

    private static function sendContactMessage($data)
    {
        return self::makeHttpRequest('/send-message', $data);
    }

    private static function sendButtonsMessage($data)
    {
        try {
            $buttons = [
                ['id' => 'order', 'title' => '🛒 اطلب الآن'],
                ['id' => 'contact', 'title' => '📞 اتصل بنا'],
                ['id' => 'more', 'title' => 'ℹ️ معلومات إضافية'],
            ];

            $btns = new WhatsappBtns(
                $buttons,
                '🎉 عرض خاص',
                'اختر أحد الأزرار بالأسفل'
            );

            $data['buttonProps'] = $btns->toArray();

            Log::info('Sending buttons message', $data);

            // return true;
            return self::makeHttpRequest('/send-message', $data);
        } catch (\Exception $e) {
            Log::error('Error sending buttons message: ' . $e->getMessage(), [
                'user_id' => $data['userId'],
                'chat_id' => $data['chatId'],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    private static function sendListMessage($data)
    {
        try {
            // Example sections structure for WhatsApp List message
            $sections = [
                [
                    'title' => 'القائمة',
                    'rows' => [
                        ['id' => 'order', 'title' => '🛒 اطلب الآن'],
                        ['id' => 'contact', 'title' => '📞 اتصل بنا'],
                        ['id' => 'more', 'title' => 'ℹ️ معلومات إضافية'],
                    ]
                ]
            ];

            $listProps = [
                'sections' => $sections,
                'buttonText' => 'اختر',
                'header' => '🎉 عرض خاص',
                'footer' => 'اختر أحد الخيارات بالأسفل'
            ];

            $data['buttonProps'] = $listProps;

            Log::info('Sending list message', $data);

            return self::makeHttpRequest('/send-message', $data);
        } catch (\Exception $e) {
            Log::error('Error sending list message: ' . $e->getMessage(), [
                'user_id' => $data['userId'],
                'chat_id' => $data['chatId'],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }


    /**
     * Generate AI response using ChatService with complete business data and message history
     * Note: If this method is called, it means WhatsApp session exists,
     * therefore business MUST exist for this user
     */
    private static function generateAIResponse($userMessage, $userId, $whatsappId = null)
    {
        try {
            $chatService = new ChatService();

            // Get complete business data from cache
            // Business MUST exist since WhatsApp session is active
            $businessData = WhatsAppCacheService::getBusinessData($userId);

            // Get conversation history formatted for AI context BEFORE adding current message
            $messageHistory = [];
            if ($whatsappId) {
                $messageHistory = WhatsAppCacheService::getConversationForAI($whatsappId);
            }
            // Prepare context with all business data and message history
            $context = [
                'user_id' => $userId,
                'business_data' => $businessData,
                'conversation_history' => $messageHistory
            ];

            // Generate response using conversation context
            $response = $chatService->generateResponse($userMessage, $context);

            // Now add both user message and AI response to cache
            WhatsAppCacheService::addMessageToCache($whatsappId, [
                'id' => $whatsappId.'_user_'.time(),
                'content' => $userMessage,
                'role' => 'user'
            ]);

            WhatsAppCacheService::addMessageToCache($whatsappId, [
                'id' => $whatsappId.'_assistant_'.time(),
                'content' => $response,
                'role' => 'assistant'
            ]);

            // طباعة الرسائل المحفوظة في الكاش لمراقبة الترتيب
            // $cachedMessages = WhatsAppCacheService::getMessageHistory($whatsappId);
            // Log::info("📋 Cached Messages:");
            // foreach ($cachedMessages as $cachedMsg) {
            //     Log::info("{$cachedMsg['role']}: {$cachedMsg['content']}");
            // }

            return $response;
        } catch (\Exception $e) {
            Log::error('WhatsApp AI Response Error: ' . $e->getMessage(), [
                'user_id' => $userId,
                'whatsapp_id' => $whatsappId,
                'message' => $userMessage
            ]);
            return 'عذراً، حدث خطأ في النظام. يرجى المحاولة لاحقاً.';
        }
    }


    public static function autoResponse($user_id, $message, $whatsapp_id, $type = 'text', $media = null, $buttonProps = null)
    {
        // TYPES ['text', 'image', 'video', 'audio', 'document', 'location', 'contact', 'buttons', 'list']
        try {

            switch ($type) {
                case 'text':
                    // Generate AI response for text messages with message history
                    $aiResponse = self::generateAIResponse($message, $user_id, $whatsapp_id);
                    self::sendTextMessage([
                        'userId' => $user_id,
                        'chatId' => $whatsapp_id,
                        'message' => $aiResponse,
                        'type' => 'text',
                        'media' => null
                    ]);
                    break;
                case 'image':
                    self::sendMediaMessage([
                        'userId' => $user_id,
                        'chatId' => $whatsapp_id,
                        'message' => $message,
                        'type' => 'image',
                        'media' => $media
                    ]);
                    break;
                case 'video':
                    self::sendMediaMessage([
                        'userId' => $user_id,
                        'chatId' => $whatsapp_id,
                        'message' => $message,
                        'type' => 'video',
                        'media' => $media
                    ]);
                    break;
                case 'audio':
                    self::sendMediaMessage([
                        'userId' => $user_id,
                        'chatId' => $whatsapp_id,
                        'message' => $message,
                        'type' => 'audio',
                        'media' => $media
                    ]);
                    break;
                case 'document':
                    self::sendMediaMessage([
                        'userId' => $user_id,
                        'chatId' => $whatsapp_id,
                        'message' => $message,
                        'type' => 'document',
                        'media' => $media
                    ]);
                    break;
                case 'location':
                    self::sendLocationMessage([
                        'userId' => $user_id,
                        'chatId' => $whatsapp_id,
                        'message' => $message,
                        'type' => 'location',
                        'media' => $media
                    ]);
                    break;
                case 'contact':
                    self::sendContactMessage([
                        'userId' => $user_id,
                        'chatId' => $whatsapp_id,
                        'message' => $message,
                        'type' => 'contact',
                        'media' => $media
                    ]);
                    break;
                case 'buttons':
                    self::sendButtonsMessage([
                        'userId' => $user_id,
                        'chatId' => $whatsapp_id,
                        'message' => $message,
                        'type' => 'buttons',
                        'media' => $media,
                        'buttonProps' => $buttonProps
                    ]);
                    break;
                case 'list':
                    self::sendListMessage([
                        'userId' => $user_id,
                        'chatId' => $whatsapp_id,
                        'message' => $message,
                        'type' => 'list',
                        'media' => $media,
                        'buttonProps' => $buttonProps
                    ]);
                    break;
            }

            Log::info('Auto response sent successfully', [
                'user_id' => $user_id,
                'message' => $message,
                'profile_id' => $whatsapp_id
            ]);
            return true;
        } catch (\Exception $e) {
            Log::error('Error sending auto response: ' . $e->getMessage(), [
                'user_id' => $user_id,
                'message' => $message,
                'profile_id' => $whatsapp_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }
};
