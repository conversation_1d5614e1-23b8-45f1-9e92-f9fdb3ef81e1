<?php

namespace App\Services\SocialMediaServices;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use App\Models\WhatsApp\WhatsAppMessage;

class WhatsAppCacheService
{
    // Cache TTL constants
    const BUSINESS_CACHE_TTL = 72000; // 20 hours in seconds
    const MESSAGES_CACHE_TTL = 300;   // 5 minutes in seconds
    const MAX_CACHED_MESSAGES = 20;   // Maximum messages to keep in cache

    // Cache key prefixes
    const BUSINESS_KEY_PREFIX = 'whatsapp:business:';
    const MESSAGES_KEY_PREFIX = 'whatsapp:messages:';

    /**
     * Get complete business data with all relations from cache or database
     * Cache for 20 hours using user_id as key
     */
    public static function getBusinessData($userId)
    {
        try {
            $cacheKey = self::BUSINESS_KEY_PREFIX . $userId;

            // Try to get from cache first
            $cachedData = Cache::get($cacheKey);

            if ($cachedData) {
                Log::info("✅ Complete business data retrieved from cache", ['user_id' => $userId]);
                return $cachedData;
            }

            // Not in cache, get from database with all relations
            Log::info("📊 Fetching complete business data from database", ['user_id' => $userId]);

            $business = \App\Models\Business::where('user_id', $userId)
                ->with(['doctors.clinic', 'clinics.doctors'])
                ->first();

            if (!$business) {
                Log::error('Business not found for user', ['user_id' => $userId]);
                return null;
            }

            // Build complete business data with all relations
            $businessData = [
                'name' => $business->name,
                'description' => $business->description ?? '',
                'phone' => $business->phone ?? '',
                'email' => $business->email ?? '',
                'address' => $business->address ?? '',
                'city' => $business->city ?? '',
                'website' => $business->website ?? '',
                'whatsapp_number' => $business->whatsapp_number ?? '',

                'doctors' => $business->doctors->map(function($doctor) {
                    return [
                        'name' => "د. {$doctor->first_name} {$doctor->last_name}",
                        'specialization' => $doctor->specialization,
                        'clinic' => $doctor->clinic->name ?? 'غير محدد',
                        'available' => $doctor->is_available ? 'متاح' : 'غير متاح',
                        'experience' => $doctor->experience ?? '',
                        'education' => $doctor->education ?? '',
                    ];
                })->toArray(),

                'clinics' => $business->clinics->map(function($clinic) {
                    return [
                        'name' => $clinic->name,
                        'description' => $clinic->description ?? '',
                        'floor' => $clinic->floor_number ?? '',
                        'room' => $clinic->room_number ?? '',
                        'phone' => $clinic->phone_number ?? '',
                        'doctors_count' => $clinic->doctors->count(),
                        'doctors' => $clinic->doctors->pluck('first_name')->toArray(),
                    ];
                })->toArray(),

                'cached_at' => now()->toISOString()
            ];

            // Store in cache for 20 hours
            Cache::put($cacheKey, $businessData, self::BUSINESS_CACHE_TTL);

            Log::info("💾 Complete business data cached successfully", [
                'user_id' => $userId,
                'doctors_count' => count($businessData['doctors']),
                'clinics_count' => count($businessData['clinics'])
            ]);

            return $businessData;

        } catch (\Exception $e) {
            Log::error('Error in getBusinessData cache: ' . $e->getMessage(), [
                'user_id' => $userId,
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Get message history from cache or database
     * Cache for 5 minutes using whatsapp_id as key
     * Maintains rolling 20-message limit
     */
    public static function getMessageHistory($whatsappId, $profileId = null)
    {
        try {
            $cacheKey = self::MESSAGES_KEY_PREFIX . $whatsappId;

            // Try to get from cache first
            $cachedMessages = Cache::get($cacheKey);

            if ($cachedMessages) {
                // Extend cache TTL on access (rolling 5 minutes)
                Cache::put($cacheKey, $cachedMessages, self::MESSAGES_CACHE_TTL);

                // Log::info("✅ Message history retrieved from cache", [
                //     'whatsapp_id' => $whatsappId,
                //     'message_count' => count($cachedMessages)
                // ]);

                return $cachedMessages;
            }
            return [];

        } catch (\Exception $e) {
            Log::error('Error in getMessageHistory cache: ' . $e->getMessage(), [
                'whatsapp_id' => $whatsappId,
                'profile_id' => $profileId,
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * Add new message to cache (append to conversation)
     * Maintains rolling conversation history with message limit
     * Each WhatsApp ID has its own conversation cache
     */
    public static function addMessageToCache($whatsappId, $messageData)
    {
        try {
            // Get current conversation from cache (using whatsappId as key)
            $currentMessages = self::getMessageHistory($whatsappId);

            // Clean and sanitize message content to handle special characters and symbols
            $content = $messageData['content'] ?? '';

            // Remove or replace problematic characters that might cause issues
            $cleanContent = self::sanitizeMessageContent($content);

            // Prepare new message for cache
            $newMessage = [
                'id' => $messageData['id'] ?? null,
                'content' => $cleanContent,
                'role' => $messageData['role']
            ];

            // Append new message to conversation
            $currentMessages[] = $newMessage;

            // Maintain rolling conversation limit (keep only recent messages)
            if (count($currentMessages) > self::MAX_CACHED_MESSAGES) {
                $currentMessages = array_slice($currentMessages, -self::MAX_CACHED_MESSAGES);

                Log::info("🔄 Conversation trimmed to maintain limit", [
                    'whatsapp_id' => $whatsappId,
                    'max_messages' => self::MAX_CACHED_MESSAGES
                ]);
            }

            // Save updated conversation to cache
            self::cacheMessages($whatsappId, $currentMessages);

            // Log::info("💬 Message appended to conversation cache", [
            //     'whatsapp_id' => $whatsappId,
            //     'total_messages' => count($currentMessages),
            //     'message_content' => $newMessage['content']
            // ]);

            return $currentMessages; // Return updated conversation

        } catch (\Exception $e) {
            Log::error('Error appending message to conversation cache: ' . $e->getMessage(), [
                'whatsapp_id' => $whatsappId,
                'message_data' => $messageData,
                'trace' => $e->getTraceAsString()
            ]);

            return []; // Return empty array on error
        }
    }

    /**
     * Get conversation formatted for AI context
     * Returns messages in a format suitable for AI processing
     */
    public static function getConversationForAI($whatsappId, $profileId = null)
    {
        try {
            $messages = self::getMessageHistory($whatsappId, $profileId);

            if (empty($messages)) {
                return [];
            }

            // Format messages for AI context
            $formattedMessages = array_map(function($message) {
                return [
                    'role' => $message['role'],
                    'content' => $message['content']
                ];
            }, $messages);

            return $formattedMessages;

        } catch (\Exception $e) {
            Log::error('Error formatting conversation for AI: ' . $e->getMessage(), [
                'whatsapp_id' => $whatsappId,
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * Cache messages with TTL
     */
    private static function cacheMessages($whatsappId, $messages)
    {
        try {
            $cacheKey = self::MESSAGES_KEY_PREFIX . $whatsappId;
            Cache::put($cacheKey, $messages, self::MESSAGES_CACHE_TTL);

            Log::info("💾 Conversation cached successfully", [
                'whatsapp_id' => $whatsappId,
                'message_count' => count($messages),
                'ttl_minutes' => self::MESSAGES_CACHE_TTL / 60,
                'cache_key' => $cacheKey
            ]);

        } catch (\Exception $e) {
            Log::error('Error caching conversation: ' . $e->getMessage(), [
                'whatsapp_id' => $whatsappId,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Clear business cache for specific user (basic data only)
     */
    public static function clearBusinessCache($userId)
    {
        try {
            $cacheKey = self::BUSINESS_KEY_PREFIX . $userId;
            Cache::forget($cacheKey);

            Log::info("🗑️ Business cache cleared", ['user_id' => $userId]);

        } catch (\Exception $e) {
            Log::error('Error clearing business cache: ' . $e->getMessage(), [
                'user_id' => $userId,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Clear conversation cache for specific WhatsApp ID
     */
    public static function clearMessageCache($whatsappId)
    {
        try {
            $cacheKey = self::MESSAGES_KEY_PREFIX . $whatsappId;
            Cache::forget($cacheKey);

            Log::info("🗑️ Conversation cache cleared", ['whatsapp_id' => $whatsappId]);

        } catch (\Exception $e) {
            Log::error('Error clearing conversation cache: ' . $e->getMessage(), [
                'whatsapp_id' => $whatsappId,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Get conversation statistics for a specific WhatsApp ID
     */
    public static function getConversationStats($whatsappId)
    {
        try {
            $messages = self::getMessageHistory($whatsappId);

            if (empty($messages)) {
                return [
                    'total_messages' => 0,
                    'user_messages' => 0,
                    'assistant_messages' => 0,
                    'last_message_at' => null,
                    'conversation_started_at' => null
                ];
            }

            $userMessages = array_filter($messages, fn($msg) => !$msg['is_outgoing']);
            $assistantMessages = array_filter($messages, fn($msg) => $msg['is_outgoing']);

            return [
                'total_messages' => count($messages),
                'user_messages' => count($userMessages),
                'assistant_messages' => count($assistantMessages),
                'last_message_at' => end($messages)['sent_at'] ?? null,
                'conversation_started_at' => $messages[0]['sent_at'] ?? null,
                'cache_key' => self::MESSAGES_KEY_PREFIX . $whatsappId
            ];

        } catch (\Exception $e) {
            Log::error('Error getting conversation stats: ' . $e->getMessage(), [
                'whatsapp_id' => $whatsappId,
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }



    /**
     * Sanitize message content to handle special characters and symbols
     * Prevents issues with encoding, JSON parsing, AI processing, and SQLite storage
     */
    private static function sanitizeMessageContent($content)
    {
        try {
            if (empty($content)) {
                return '';
            }

            // Convert to string if not already
            $content = (string) $content;

            // Ensure UTF-8 encoding
            if (!mb_check_encoding($content, 'UTF-8')) {
                $content = mb_convert_encoding($content, 'UTF-8', 'auto');
            }

            // Remove null bytes and other control characters that might cause SQLite issues
            $content = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $content);

            // Remove or replace problematic characters for SQLite
            $content = str_replace([
                "\0",     // null byte
                "\x1A",   // substitute character
                "\xEF\xBF\xBD", // replacement character
            ], '', $content);

            // Clean up multiple whitespaces
            $content = preg_replace('/\s+/', ' ', $content);
            $content = trim($content);

            // Handle emojis and special Unicode characters safely
            // Convert problematic Unicode to safe alternatives if needed
            $content = self::handleUnicodeCharacters($content);

            // Limit message length to prevent memory issues
            if (mb_strlen($content) > 4000) {
                $content = mb_substr($content, 0, 4000) . '... [رسالة مقطوعة]';
                Log::warning('Message content truncated due to length', [
                    'original_length' => mb_strlen($content),
                    'truncated_length' => mb_strlen($content)
                ]);
            }

            return $content;

        } catch (\Exception $e) {
            Log::error('Error sanitizing message content: ' . $e->getMessage(), [
                'content_preview' => mb_substr($content ?? '', 0, 100),
                'trace' => $e->getTraceAsString()
            ]);

            // Return safe fallback
            return '[رسالة تحتوي على رموز غير مدعومة]';
        }
    }

    /**
     * Handle Unicode characters safely for SQLite storage
     */
    private static function handleUnicodeCharacters($content)
    {
        try {
            // Check if content contains valid UTF-8
            if (!mb_check_encoding($content, 'UTF-8')) {
                return '[رسالة تحتوي على ترميز غير صحيح]';
            }

            // Remove any remaining problematic characters
            $content = filter_var($content, FILTER_UNSAFE_RAW, FILTER_FLAG_STRIP_LOW | FILTER_FLAG_STRIP_HIGH);

            // If filter_var removed too much, fall back to original with basic cleaning
            if (empty($content) || mb_strlen($content) < 3) {
                $content = preg_replace('/[^\p{L}\p{N}\p{P}\p{Z}\p{S}\p{M}]/u', '', $content);
            }

            return $content;

        } catch (\Exception $e) {
            Log::error('Error handling Unicode characters: ' . $e->getMessage());
            return '[رسالة تحتوي على رموز معقدة]';
        }
    }

    /**
     * Get cache statistics (basic data only)
     */
    public static function getCacheStats()
    {
        try {
            // Note: Cache facade doesn't have keys() method like Redis
            // This is a simplified version for database cache
            return [
                'business_cache_count' => 'N/A (using database cache)',
                'message_cache_count' => 'N/A (using database cache)',
                'total_cache_keys' => 'N/A (using database cache)'
            ];

        } catch (\Exception $e) {
            Log::error('Error getting cache stats: ' . $e->getMessage());
            return [
                'business_cache_count' => 0,
                'message_cache_count' => 0,
                'total_cache_keys' => 0
            ];
        }
    }
}
