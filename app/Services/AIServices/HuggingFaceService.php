<?php

namespace App\Services\AIServices;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class HuggingFaceService extends BaseAIService
{
    public function __construct()
    {
        parent::__construct('Hugging Face');
    }

    protected function getDefaultHeaders(): array
    {
        return [
            'Authorization' => 'Bearer ' . $this->apiKey,
            'Content-Type' => 'application/json',
        ];
    }

    public function chat(string $message, $isResponder = false, ?array $options = []): array
    {
        if (!isset($options['model'])) {
            throw new \InvalidArgumentException('Model is required. Please specify a model name.');
        }

        $messages = [];

         // Get system message from model database and add it to the system message if provided
        $systemMessage = $this->getModelSystemMessage($options['model'], 'chat');
        if (isset($options['system_message'])) {
            $systemMessage = $systemMessage . "\n" . $options['system_message'];
        }
        if ($systemMessage) {
            $messages[] = [
                'role' => 'system',
                'content' => $systemMessage
            ];
        }

        // Add conversation history if provided - Only for responder models
        if ($isResponder && isset($options['conversation_history']) && is_array($options['conversation_history'])) {
            $messages = array_merge($messages, $options['conversation_history']);
        }

        // Add user message
        $messages[] = [
            'role' => 'user',
            'content' => $message
        ];

        // Hugging Face Router now uses OpenAI-compatible format
        $data = [
            'model' => $options['model'],
            'messages' => $messages,
            'max_tokens' => $options['max_tokens']??$this->getModelMaxTokens($options['model'], 'chat') ?? 1000,
            'temperature' => $options['temperature']??$this->getModelTemperature($options['model'], 'chat') ?? 0.7,
            'top_p' => $options['top_p'] ?? 0.9,
        ];

        return $this->makeRequest('/chat/completions', $data);
    }

    public function generateImage(string $prompt, ?string $model = null, array $options = []): array
    {
        if (!$model) {
            throw new \InvalidArgumentException('Model is required. Please specify a model name.');
        }

        // Get dynamic options based on model capabilities
        $dynamicOptions = $this->getDefaultImageOptions($model, $options);

        // Parse size if available (format: WIDTHxHEIGHT)
        $width = $options['width'] ?? 1024;
        $height = $options['height'] ?? 1024;

        if (isset($dynamicOptions['size']) && preg_match('/^(\d+)x(\d+)$/', $dynamicOptions['size'], $matches)) {
            $width = (int)$matches[1];
            $height = (int)$matches[2];
        }

        $data = [
            'inputs' => $prompt,
            'parameters' => [
                'num_inference_steps' => $options['steps'] ?? 50,
                'guidance_scale' => $options['guidance_scale'] ?? 7.5,
                'width' => $width,
                'height' => $height,
            ],
            'options' => [
                'wait_for_model' => $options['wait_for_model'] ?? true,
                'use_cache' => $options['use_cache'] ?? true,
            ]
        ];

        return $this->makeRequest("/models/{$model}", $data);
    }

    public function editImage(string $imageUrl, string $prompt, ?string $model = null, array $options = []): array
    {
        if (!$model) {
            throw new \InvalidArgumentException('Model is required. Please specify a model name.');
        }

        // Get dynamic options based on model capabilities
        $dynamicOptions = $this->getDefaultImageOptions($model, $options);

        $data = [
            'inputs' => [
                'image' => $imageUrl,
                'text' => $prompt,
            ],
            'parameters' => [
                'num_inference_steps' => $options['steps'] ?? 20,
                'image_guidance_scale' => $options['image_guidance_scale'] ?? 1.5,
                'guidance_scale' => $options['guidance_scale'] ?? 7.5,
            ],
            'options' => [
                'wait_for_model' => $options['wait_for_model'] ?? true,
                'use_cache' => $options['use_cache'] ?? true,
            ]
        ];

        // Add size-based parameters if available
        if (isset($dynamicOptions['size']) && preg_match('/^(\d+)x(\d+)$/', $dynamicOptions['size'], $matches)) {
            $data['parameters']['width'] = (int)$matches[1];
            $data['parameters']['height'] = (int)$matches[2];
        }

        return $this->makeRequest("/models/{$model}", $data);
    }

    public function generateVideo(string $prompt, ?string $model = null, array $options = []): array
    {
        if (!$model) {
            throw new \InvalidArgumentException('Model is required. Please specify a model name.');
        }

        $data = [
            'inputs' => $prompt,
            'parameters' => [
                'num_frames' => $options['num_frames'] ?? 16,
                'num_inference_steps' => $options['steps'] ?? 25,
                'guidance_scale' => $options['guidance_scale'] ?? 9.0,
            ],
            'options' => [
                'wait_for_model' => $options['wait_for_model'] ?? true,
                'use_cache' => $options['use_cache'] ?? false, // Video generation usually shouldn't be cached
            ]
        ];

        return $this->makeRequest("/models/{$model}", $data);
    }

    public function getModelInfo(string $model): array
    {
        return $this->makeGetRequest("/models/{$model}");
    }

    public function textClassification(string $text, ?string $model = null, array $options = []): array
    {
        if (!$model) {
            throw new \InvalidArgumentException('Model is required. Please specify a model name.');
        }

        $data = [
            'inputs' => $text,
            'options' => [
                'wait_for_model' => $options['wait_for_model'] ?? true,
                'use_cache' => $options['use_cache'] ?? true,
            ]
        ];

        return $this->makeRequest("/models/{$model}", $data);
    }

    public function textSummarization(string $text, ?string $model = null, array $options = []): array
    {
        if (!$model) {
            throw new \InvalidArgumentException('Model is required. Please specify a model name.');
        }

        $data = [
            'inputs' => $text,
            'parameters' => [
                'max_length' => $options['max_length'] ?? 150,
                'min_length' => $options['min_length'] ?? 30,
                'do_sample' => $options['do_sample'] ?? false,
            ],
            'options' => [
                'wait_for_model' => $options['wait_for_model'] ?? true,
                'use_cache' => $options['use_cache'] ?? true,
            ]
        ];

        return $this->makeRequest("/models/{$model}", $data);
    }

    public function questionAnswering(string $question, string $context, ?string $model = null, array $options = []): array
    {
        if (!$model) {
            throw new \InvalidArgumentException('Model is required. Please specify a model name.');
        }

        $data = [
            'inputs' => [
                'question' => $question,
                'context' => $context,
            ],
            'options' => [
                'wait_for_model' => $options['wait_for_model'] ?? true,
                'use_cache' => $options['use_cache'] ?? true,
            ]
        ];

        return $this->makeRequest("/models/{$model}", $data);
    }

    public function textEmbedding(string $text, ?string $model = null, array $options = []): array
    {
        if (!$model) {
            throw new \InvalidArgumentException('Model is required. Please specify a model name.');
        }

        $data = [
            'inputs' => $text,
            'options' => [
                'wait_for_model' => $options['wait_for_model'] ?? true,
                'use_cache' => $options['use_cache'] ?? true,
            ]
        ];

        return $this->makeRequest("/models/{$model}", $data);
    }

    protected function makeRequest(string $endpoint, array $data, array $headers = []): array
    {
        try {
            $request = Http::withHeaders(array_merge($this->getDefaultHeaders(), $headers))
                ->timeout(120); // Longer timeout for HF models

            $response = $request->post($this->baseUrl . $endpoint, $data);

            if ($response->successful()) {
                return $response->json() ?? ['data' => $response->body()];
            }

            Log::error("AI Service Request Failed", [
                'provider' => $this->provider->name,
                'endpoint' => $endpoint,
                'status' => $response->status(),
                'response' => $response->body()
            ]);

            throw new \Exception("Request failed with status: {$response->status()}");
        } catch (\Exception $e) {
            Log::error("AI Service Exception", [
                'provider' => $this->provider->name,
                'endpoint' => $endpoint,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    protected function makeGetRequest(string $endpoint, array $headers = []): array
    {
        try {
            $request = Http::withHeaders(array_merge($this->getDefaultHeaders(), $headers))
                ->timeout(120);

            $response = $request->get($this->baseUrl . $endpoint);

            if ($response->successful()) {
                return $response->json() ?? ['data' => $response->body()];
            }

            throw new \Exception("Request failed with status: {$response->status()}");
        } catch (\Exception $e) {
            throw $e;
        }
    }
}
