<?php

namespace App\Services\AIServices;

use Illuminate\Support\Facades\Log;

class DeepSeekService extends BaseAIService
{
    public function __construct()
    {
        parent::__construct('DeepSeek');
    }

    protected function getDefaultHeaders(): array
    {
        return [
            'Authorization' => 'Bearer ' . $this->apiKey,
            'Content-Type' => 'application/json',
        ];
    }

    public function chat(string $message, $isResponder = false, ?array $options = []): array
    {
        if (!isset($options['model'])) {
            throw new \InvalidArgumentException('Model is required. Please specify a model name.');
        }

        // تنظيف الرسالة من الرموز الضارة
        $cleanMessage = $this->sanitizeMessageForAPI($message);

        $messages = [];

       // Get system message from model database and add it to the system message if provided
        $systemMessage = $this->getModelSystemMessage($options['model'], 'chat');
        if (isset($options['system_message'])) {
            $systemMessage = $systemMessage . "\n" . $options['system_message'];
        }

        if ($systemMessage) {
            $messages[] = [
                'role' => 'system',
                'content' => $systemMessage
            ];
        }

        // Add conversation history if provided - Only for responder models
        if ($isResponder && isset($options['conversation_history']) && is_array($options['conversation_history'])) {
            // تنظيف رسائل المحادثة السابقة
            $cleanHistory = array_map(function($msg) {
                return [
                    'role' => $msg['role'],
                    'content' => $this->sanitizeMessageForAPI($msg['content'])
                ];
            }, $options['conversation_history']);

            $messages = array_merge($messages, $cleanHistory);
        }

        // Add user message
        $messages[] = [
            'role' => 'user',
            'content' => $cleanMessage
        ];

        $data = [
            'model' => $options['model'],
            'messages' => $messages,
            'max_tokens' => $options['max_tokens']??$this->getModelMaxTokens($options['model'], 'chat') ?? 1000,
            'temperature' => $options['temperature']??$this->getModelTemperature($options['model'], 'chat') ?? 0.7,
            'top_p' => $options['top_p'] ?? 1,
            'frequency_penalty' => $options['frequency_penalty'] ?? 0,
            'presence_penalty' => $options['presence_penalty'] ?? 0,
        ];

        $response = $this->makeRequest('/v1/chat/completions', $data);

        // Parse and validate DeepSeek response (uses OpenAI format)
        $aiResponse = $this->parseDeepSeekResponse($response);

        Log::info('🤖 DeepSeek Chat Request Data:', [
            'model' => $options['model'],
            'message' => $message,
            'aiResponse' => $aiResponse,
            'Messagessssssss'=>$messages
        ]);

        return $aiResponse;
    }

    /**
     * Parse DeepSeek-format response and extract text content
     */
    private function parseDeepSeekResponse(array $response): array
    {
        // Check for successful response with content
        if (isset($response['choices'][0]['message']['content'])) {
            return [
                'success' => true,
                'content' => $response['choices'][0]['message']['content'],
                'raw_response' => $response
            ];
        }

        // Check for errors in response
        if (isset($response['error'])) {
            return [
                'success' => false,
                'error' => $response['error']['type'] ?? 'unknown',
                'message' => $response['error']['message'] ?? 'حدث خطأ غير متوقع.',
                'raw_response' => $response
            ];
        }

        // No valid response found
        return [
            'success' => false,
            'error' => 'INVALID_RESPONSE',
            'message' => 'عذراً، لم أتمكن من فهم رسالتك. يرجى المحاولة مرة أخرى.',
            'raw_response' => $response
        ];
    }

    public function generateImage(string $prompt, ?string $model = null, array $options = []): array
    {
        if (!$model) {
            throw new \InvalidArgumentException('Model is required. Please specify a model name.');
        }

        // Get dynamic options based on model capabilities
        $dynamicOptions = $this->getDefaultImageOptions($model, $options);

        $data = [
            'model' => $model,
            'prompt' => $prompt,
            'n' => $options['n'] ?? 1,
            'response_format' => $options['response_format'] ?? 'url',
        ];

        // Add size if available
        if (isset($dynamicOptions['size'])) {
            $data['size'] = $dynamicOptions['size'];
        }

        // Add quality if available
        if (isset($dynamicOptions['quality'])) {
            $data['quality'] = $dynamicOptions['quality'];
        }

        return $this->makeRequest('/v1/images/generations', $data);
    }

    public function editImage(string $imageUrl, string $prompt, ?string $model = null, array $options = []): array
    {
        if (!$model) {
            throw new \InvalidArgumentException('Model is required. Please specify a model name.');
        }

        // Get dynamic options based on model capabilities
        $dynamicOptions = $this->getDefaultImageOptions($model, $options);

        $data = [
            'model' => $model,
            'image' => $imageUrl,
            'prompt' => $prompt,
            'n' => $options['n'] ?? 1,
            'response_format' => $options['response_format'] ?? 'url',
        ];

        // Add size if available
        if (isset($dynamicOptions['size'])) {
            $data['size'] = $dynamicOptions['size'];
        }

        if (isset($options['mask'])) {
            $data['mask'] = $options['mask'];
        }

        return $this->makeRequest('/v1/images/edits', $data);
    }

    public function generateVideo(string $prompt, ?string $model = null, array $options = []): array
    {
        if (!$model) {
            throw new \InvalidArgumentException('Model is required. Please specify a model name.');
        }

        $data = [
            'model' => $model,
            'prompt' => $prompt,
            'duration' => $options['duration'] ?? 5, // seconds
            'fps' => $options['fps'] ?? 24,
            'resolution' => $options['resolution'] ?? '1024x576',
            'quality' => $options['quality'] ?? 'standard',
        ];

        return $this->makeRequest('/v1/videos/generations', $data);
    }

    public function getModels(): array
    {
        return $this->makeRequest('/v1/models', []);
    }

    public function getUsage(array $options = []): array
    {
        $params = [];

        if (isset($options['start_date'])) {
            $params['start_date'] = $options['start_date'];
        }

        if (isset($options['end_date'])) {
            $params['end_date'] = $options['end_date'];
        }

        $queryString = !empty($params) ? '?' . http_build_query($params) : '';

        return $this->makeRequest('/v1/usage' . $queryString, []);
    }

    public function streamChat(string $message, ?string $model = null, array $options = []): array
    {
        if (!$model) {
            throw new \InvalidArgumentException('Model is required. Please specify a model name.');
        }

        $data = [
            'model' => $model,
            'messages' => [
                [
                    'role' => 'user',
                    'content' => $message
                ]
            ],
            'max_tokens' => $options['max_tokens'] ?? $this->getModelMaxTokens($model, 'chat') ?? 1000,
            'temperature' => $options['temperature'] ?? $this->getModelTemperature($model, 'chat') ?? 0.7,
            'stream' => true,
        ];

        // Add conversation history if provided
        if (isset($options['conversation_history']) && is_array($options['conversation_history'])) {
            $data['messages'] = array_merge($options['conversation_history'], $data['messages']);
        }

        return $this->makeRequest('/v1/chat/completions', $data);
    }

    public function validateApiKey(): array
    {
        return $this->makeRequest('/v1/models', []);
    }
}
