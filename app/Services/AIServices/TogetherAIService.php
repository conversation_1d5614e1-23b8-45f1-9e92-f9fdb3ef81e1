<?php

namespace App\Services\AIServices;

class TogetherAIService extends BaseAIService
{
    public function __construct()
    {
        parent::__construct('Togather AI');
    }

    protected function getDefaultHeaders(): array
    {
        return [
            'Authorization' => 'Bearer ' . $this->apiKey,
            'Content-Type' => 'application/json',
        ];
    }

    public function chat(string $message, ?array $options = []): array
    {
        if (!isset($options['model'])) {
            throw new \InvalidArgumentException('Model is required. Please specify a model name.');
        }

        $messages = [];

      // Get system message from model database and add it to the system message if provided
        $systemMessage = $this->getModelSystemMessage($options['model'], 'chat');
        if (isset($options['system_message'])) {
            $systemMessage = $systemMessage . "\n" . $options['system_message'];
        }
        if ($systemMessage) {
            $messages[] = [
                'role' => 'system',
                'content' => $systemMessage
            ];
        }

        // Add conversation history if provided
        if (isset($options['conversation_history']) && is_array($options['conversation_history'])) {
            $messages = array_merge($messages, $options['conversation_history']);
        }

        // Add user message
        $messages[] = [
            'role' => 'user',
            'content' => $message
        ];

        $data = [
            'model' => $options['model'],
            'messages' => $messages,
            'max_tokens' => $options['max_tokens']??$this->getModelMaxTokens($options['model'], 'chat') ?? 1000,
            'temperature' => $options['temperature']??$this->getModelTemperature($options['model'], 'chat') ?? 0.7,
            'top_p' => $options['top_p'] ?? 1,
            'frequency_penalty' => $options['frequency_penalty'] ?? 0,
            'presence_penalty' => $options['presence_penalty'] ?? 0,
        ];

        // Add system message if provided
        if (isset($options['system_message'])) {
            array_unshift($data['messages'], [
                'role' => 'system',
                'content' => $options['system_message']
            ]);
        }

        return $this->makeRequest('/chat/completions', $data);
    }

    public function generateImage(string $prompt, ?string $model = null, array $options = []): array
    {
        // Together AI doesn't have image generation models in your seeder
        throw new \Exception('Image generation not available for Together AI models');
    }

    public function editImage(string $imageUrl, string $prompt, ?string $model = null, array $options = []): array
    {
        // Together AI doesn't have image editing models in your seeder
        throw new \Exception('Image editing not available for Together AI models');
    }

    public function generateVideo(string $prompt, ?string $model = null, array $options = []): array
    {
        // Together AI doesn't have video generation models in your seeder
        throw new \Exception('Video generation not available for Together AI models');
    }

    public function getModels(): array
    {
        return $this->makeRequest('/models', []);
    }

    public function streamChat(string $message, ?string $model = null, array $options = []): array
    {
        if (!$model) {
            throw new \InvalidArgumentException('Model is required. Please specify a model name.');
        }

        $data = [
            'model' => $model,
            'messages' => [
                [
                    'role' => 'user',
                    'content' => $message
                ]
            ],
            'max_tokens' => $options['max_tokens'] ?? $this->getModelMaxTokens($model, 'chat') ?? 1000,
            'temperature' => $options['temperature'] ?? $this->getModelTemperature($model, 'chat') ?? 0.7,
            'stream' => true,
        ];

        // Add conversation history if provided
        if (isset($options['conversation_history']) && is_array($options['conversation_history'])) {
            $data['messages'] = array_merge($options['conversation_history'], $data['messages']);
        }

        return $this->makeRequest('/chat/completions', $data);
    }

    public function getUsage(): array
    {
        return $this->makeRequest('/usage', []);
    }
}
