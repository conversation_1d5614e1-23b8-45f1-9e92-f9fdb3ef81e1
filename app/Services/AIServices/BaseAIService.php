<?php

namespace App\Services\AIServices;

use App\Models\AiProvider;
use App\Models\AiModel;
use App\Models\ApiKey;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

abstract class BaseAIService
{
    protected $provider;
    protected $apiKey;
    protected $baseUrl;

    public function __construct(string $providerName)
    {
        $this->provider = AiProvider::where('name', $providerName)
            ->where('status', 'active')
            ->first();

        if (!$this->provider) {
            throw new \Exception("AI Provider '{$providerName}' not found or inactive");
        }

        $this->baseUrl = $this->provider->base_url;
        $this->setApiKey();
    }

    protected function setApiKey()
    {
        $apiKey = ApiKey::where('ai_provider_id', $this->provider->id)
            ->where('status', 'active')
            ->first();

        if (!$apiKey) {
            throw new \Exception("No active API key found for provider: {$this->provider->name}");
        }

        $this->apiKey = $apiKey->api_key;
    }

    public function getAvailableModels(array $capabilities = [])
    {
        $query = AiModel::where('ai_provider_id', $this->provider->id)
            ->where('status', 'active');

        if (!empty($capabilities)) {
            $query->whereHas('capabilities', function ($q) use ($capabilities) {
                $q->whereIn('name', $capabilities);
            });
        }

        return $query->with('capabilities')->get();
    }

    public function getChatModels()
    {
        return $this->getAvailableModels(['chat']);
    }

    public function getImageGeneratesModels()
    {
        return $this->getAvailableModels(['image_generates']);
    }

    public function getImageEditsModels()
    {
        return $this->getAvailableModels(['image_edits']);
    }

    public function getVideoGeneratingModels()
    {
        return $this->getAvailableModels(['video_generating']);
    }

    /**
     * Get the system message for a specific model and capability
     */
    public function getModelSystemMessage(string $modelName, string $capability = 'chat'): ?string
    {
        $model = AiModel::where('ai_provider_id', $this->provider->id)
            ->where('name', $modelName)
            ->where('status', 'active')
            ->with('capabilities')
            ->first();

        if (!$model) {
            return null;
        }

        $systemMessage = $model->getSystemMessageForCapability($capability);

        // Return null if system message is empty string
        return (!empty($systemMessage)) ? $systemMessage : null;
    }

    /**
     * Get model details including capabilities
     */
    public function getModelDetails(string $modelName): ?AiModel
    {
        return AiModel::where('ai_provider_id', $this->provider->id)
            ->where('name', $modelName)
            ->where('status', 'active')
            ->with('capabilities')
            ->first();
    }

    /**
     * Check if model supports a specific capability
     */
    public function modelSupportsCapability(string $modelName, string $capability): bool
    {
        $model = $this->getModelDetails($modelName);
        return $model ? $model->hasCapability($capability) : false;
    }

    /**
     * Get max tokens for a specific model and capability
     */
    public function getModelMaxTokens(string $modelName, string $capability = 'chat'): ?int
    {
        $model = $this->getModelDetails($modelName);
        return $model ? $model->getMaxTokensForCapability($capability) : null;
    }

    /**
     * Get temperature for a specific model and capability
     */
    public function getModelTemperature(string $modelName, string $capability = 'chat'): ?float
    {
        $model = $this->getModelDetails($modelName);
        return $model ? $model->getTemperatureForCapability($capability) : null;
    }

    /**
     * Get available image details (sizes/quality) for a specific model
     */
    public function getModelImageDetails(string $modelName): ?array
    {
        $model = AiModel::where('ai_provider_id', $this->provider->id)
            ->where('name', $modelName)
            ->where('status', 'active')
            ->first();

        return $model ? $model->img_details : null;
    }

    /**
     * Get default image options based on model capabilities
     */
    protected function getDefaultImageOptions(string $modelName, array $userOptions = []): array
    {
        $modelDetails = $this->getModelImageDetails($modelName);
        $options = [];

        if (!$modelDetails) {
            return $userOptions;
        }

        // Check if details contain sizes (format: WIDTHxHEIGHT)
        $sizes = array_filter($modelDetails, function($detail) {
            return preg_match('/^\d+x\d+$/', $detail);
        });

        // Check if details contain quality levels
        $qualities = array_filter($modelDetails, function($detail) {
            return in_array(strtolower($detail), ['auto', 'low', 'medium', 'high', 'standard', 'hd']);
        });

        // Set default size if available and not provided by user
        if (!empty($sizes) && !isset($userOptions['size'])) {
            $options['size'] = $sizes[0]; // Use first available size as default
        }

        // Set default quality if available and not provided by user
        if (!empty($qualities) && !isset($userOptions['quality'])) {
            $options['quality'] = $qualities[0]; // Use first available quality as default
        }

        return array_merge($options, $userOptions);
    }


    protected function makeRequest(string $endpoint, array $data, array $headers = [])
    {
        try {
            $response = Http::withHeaders(array_merge($this->getDefaultHeaders(), $headers))
                ->timeout(60)
                ->post($this->baseUrl . $endpoint, $data);

            if ($response->successful()) {
                return $response->json();
            }

            Log::error("AI Service Request Failed", [
                'provider' => $this->provider->name,
                'endpoint' => $endpoint,
                'status' => $response->status(),
                'response' => $response->body()
            ]);

            throw new \Exception("Request failed with status: {$response->status()}");
        } catch (\Exception $e) {
            Log::error("AI Service Exception", [
                'provider' => $this->provider->name,
                'endpoint' => $endpoint,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    abstract protected function getDefaultHeaders(): array;
    abstract public function chat(string $message,$isResponder = false, ?array $options = []): array;
    abstract public function generateImage(string $prompt, ?string $model = null, array $options = []): array;
    abstract public function editImage(string $imageUrl, string $prompt, ?string $model = null, array $options = []): array;
    abstract public function generateVideo(string $prompt, ?string $model = null, array $options = []): array;
}
