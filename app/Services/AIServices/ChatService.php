<?php

namespace App\Services\AIServices;

use App\Helpers\ClinicQueryHelper;
use App\Helpers\DoctorQueryHelper;
use App\Services\AIServices\AIServiceFactory;

class ChatService
{
    private $analyzer = [
        'provider' => 'google',
        'model' => 'gemma-3-12b-it',
        'max_tokens' => 100,
        'temperature' => 0.1,
    ];

    // private $searcher = [
    //     'provider' => 'google',
    //     'model' => 'gemma-3n-e2b-it',
    //     'max_tokens' => 500,
    //     'temperature' => 0.2,
    // ];

    private $responder = [
        'provider' => 'google',
        'model' => 'gemini-2.5-flash-lite-preview-06-17',
        'max_tokens' => 300,
        'temperature' => 0.7,
    ];

    /**
     * تحليل طلب العميل وإرجاع JSON للرد
     */
    public function generateResponse(string $userMessage, array $context = []): string
    {
        try {

            $analysis = $this->analyzeRequest($userMessage, $context);

            // 2. البحث في البيانات (بدون سياق - بحث في البيانات فقط)
            // $searchResult = $this->searchBusinessData($analysis, $context);

            // 3. إنشاء الرد النهائي (مع السياق - هنا نحتاج المحادثات السابقة)
            $response = $this->generateFinalResponse($userMessage, $analysis, $context);

            return $response;
        } catch (\Exception $e) {
            return 'عذراً، حدث خطأ في النظام. يرجى المحاولة لاحقاً.';
        }
    }

    /**
     * 1. تحليل الطلب
     */
    private function analyzeRequest(string $userMessage, array $context): string
    {
        $aiService = AIServiceFactory::create($this->analyzer['provider']);
        $businessData = $context['business_data'] ?? [];

        $prompt = "حلل الطلب وحدد ماذا يريد العميل:\n\n";
        $prompt .= "رسالة العميل: \"{$userMessage}\"\n\n";
        $prompt .= "اذا سئل عن خدمه معينه جيب بيانات الخدمه هذه او سئل عن طبيب او يشتي يعرف الأطباء في عيادة معينه,\n";
        $prompt .= "او استفسار عن العيادات او حجز موعد,اي شيء انت فقط افهم سؤاله وجاوبه بالبيانات حق البزنز.\n";
        $prompt .= "أرجع فقط البيانات المطلوبه فقط بشكل منظم ومختصر :\n";
        $prompt .= "بيانات البزنس:\n" . json_encode($businessData, JSON_UNESCAPED_UNICODE) . "\n\n";
        $prompt .= "اذا لم تجد شيء فقط ارجع جملة مفيده انو العميل لم يوضح طلبه";
        $response = $aiService->chat($prompt, false, $this->analyzer);
        return $response['success'] ? trim($response['content']) : 'محادثة_عامة';
    }

    /**
     * 3. إنشاء الرد النهائي
     */
    private function generateFinalResponse(string $userMessage, string $searchResult, array $context): string
    {
        $aiService = AIServiceFactory::create($this->responder['provider']);

        $businessName = $context['business_data']['name'] ?? 'العيادة';

        $prompt = "أنت موظف في {$businessName}.\n";
        $prompt .= "مهمتك الرد على استفسارات العملاء بأسلوب مهذب، ودود، ومفيد.\n";
        $prompt .= "- اكتب الرد باللغة العربية، باللهجة السعودية البسيطة.\n";
        $prompt .= "- خلك لبِق، واضح، ولا تتجاوز 200 كلمة.\n";
        $prompt .= "- لا تكرر المعلومات، وركّز على إفادة العميل بناءً على التحليل.\n";
        $prompt .= "- إذا ما كان فيه معلومات كافية، اعتذر بلُطف واقترح طريقة للمساعدة.\n\n";
        $prompt .= "البيانات المتاحة:\n{$searchResult}\n\n";
        $prompt .= "لو قد رحبت بالعميل في الرسائل السابقه خلاص لاتكرر الترحيب.\n";
        $prompt .= "اكتب رد مهذب ومفيد باللهجة السعودية. لا تتجاوز 200 كلمة.";

        $systemMessage = $prompt;

        $response = $aiService->chat($userMessage, true, [
            'model' => $this->responder['model'],
            'temperature' => $this->responder['temperature'],
            'max_tokens' => $this->responder['max_tokens'],
            'conversation_history' => $context['conversation_history'] ?? [],
            'system_message' => $systemMessage
        ]);

        return $response['success'] ? $response['content'] : 'مرحبا عميلنا العزيز لقد تلقينا رسالتك وسيتم الرد بأقرب وقت ممكن';
    }
}
