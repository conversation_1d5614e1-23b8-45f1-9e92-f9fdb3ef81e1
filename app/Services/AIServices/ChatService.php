<?php

namespace App\Services\AIServices;

use App\Helpers\ClinicQueryHelper;
use App\Helpers\DoctorQueryHelper;
use App\Services\AIServices\AIServiceFactory;

class ChatService
{
    private $analyzer = [
        'provider' => 'google',
        'model' => 'gemma-3-12b-it',
        'max_tokens' => 100,
        'temperature' => 0.1,
    ];

    // private $searcher = [
    //     'provider' => 'google',
    //     'model' => 'gemma-3n-e2b-it',
    //     'max_tokens' => 500,
    //     'temperature' => 0.2,
    // ];

    private $responder = [
        'provider' => 'deepseek',
        'model' => 'deepseek-chat',
        'max_tokens' => 300,
        'temperature' => 0.7,
    ];

    /**
     * تحليل طلب العميل وإرجاع JSON للرد
     */
    public function generateResponse(string $userMessage, array $context = []): string
    {
        try {
            // تنظيف وحماية الرسالة من الرموز والحروف الغريبة
            $userMessage = $this->sanitizeMessage($userMessage);

            $analysis = $this->analyzeRequest($userMessage, $context);

            // 2. البحث في البيانات (بدون سياق - بحث في البيانات فقط)
            // $searchResult = $this->searchBusinessData($analysis, $context);

            // 3. إنشاء الرد النهائي (مع السياق - هنا نحتاج المحادثات السابقة)
            $response = $this->generateFinalResponse($userMessage, $analysis, $context);

            return $response;
        } catch (\Exception $e) {
            return 'عذراً، حدث خطأ في النظام. يرجى المحاولة لاحقاً.';
        }
    }

    /**
     * 1. تحليل الطلب
     */
    private function analyzeRequest(string $userMessage, array $context): string
    {
        $aiService = AIServiceFactory::create($this->analyzer['provider']);
        $businessData = $context['business_data'] ?? [];

        $prompt = "حلل الطلب وحدد ماذا يريد العميل:\n\n";
        $prompt .= "رسالة العميل: \"{$userMessage}\"\n\n";
        $prompt .= "اذا سئل عن خدمه معينه جيب بيانات الخدمه هذه او سئل عن طبيب او يشتي يعرف الأطباء في عيادة معينه,\n";
        $prompt .= "او استفسار عن العيادات او حجز موعد,اي شيء انت فقط افهم سؤاله وجاوبه بالبيانات حق البزنز.\n";
        $prompt .= "أرجع فقط البيانات المطلوبه فقط بشكل منظم ومختصر :\n";
        $prompt .= "بيانات البزنس:\n" . json_encode($businessData, JSON_UNESCAPED_UNICODE) . "\n\n";
        $prompt .= "اذا سئل عن معلومات تخصك مثلا ايش اسمك او من انت رد انو العميل يشتي يعرف عنك اكثر كلمه انك موظف الأستقبال في {$businessData['name']}.\n";
        $prompt .= "اذا لم تجد شيء فقط ارجع جملة مفيده انو العميل لم يوضح طلبه";


        $response = $aiService->chat($prompt, false, $this->analyzer);
        return $response['success'] ? trim($response['content']) : 'محادثة_عامة';
    }

    /**
     * 3. إنشاء الرد النهائي
     */
    private function generateFinalResponse(string $userMessage, string $searchResult, array $context): string
    {
        $aiService = AIServiceFactory::create($this->responder['provider']);

        $businessName = $context['business_data']['name'] ?? 'العيادة';


        $prompt = "البيانات المتاحة:\n{$searchResult}\n\n";
        $prompt .= "رسالة العميل: \"{$userMessage}";


        $systemMessage = "أنت موظف في {$businessName}.\n";
        $systemMessage .= "مهمتك الرد على استفسارات العملاء بأسلوب ودود، ومفيد.\n";
        $systemMessage .= "- اكتب الرد باللغة العربية، باللهجة السعودية البسيطة.\n";
        $systemMessage .= "- خلك لطيف، واضح، ولا تتجاوز 200 كلمة.\n";
        $systemMessage .= "- لا تكرر المعلومات،والتحيه خاصة لو قد حييت العميل بالرسائل السابقه, وركّز على إفادة العميل بناءً على التحليل.\n";
        $systemMessage .= "- إذا ما كان فيه معلومات كافية، اعتذر بلُطف واقترح طريقة للمساعدة.\n\n";

        $response = $aiService->chat($prompt, true, [
            'model' => $this->responder['model'],
            'temperature' => $this->responder['temperature'],
            'max_tokens' => $this->responder['max_tokens'],
            'conversation_history' => $context['conversation_history'] ?? [],
            'system_message' => $systemMessage
        ]);

        return $response['success'] ? $response['content'] : 'مرحبا عميلنا العزيز لقد تلقينا رسالتك وسيتم الرد بأقرب وقت ممكن';
    }

    /**
     * تنظيف وحماية الرسالة من الرموز والحروف الغريبة
     */
    private function sanitizeMessage(string $message): string
    {
        try {
            if (empty($message)) {
                return '';
            }

            // التأكد من UTF-8 encoding
            if (!mb_check_encoding($message, 'UTF-8')) {
                $message = mb_convert_encoding($message, 'UTF-8', 'auto');
            }

            // إزالة الرموز الضارة والتحكم characters
            $message = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $message);

            // إزالة المسافات الزائدة
            $message = trim($message);
            $message = preg_replace('/\s+/', ' ', $message);

            // تحديد طول الرسالة لمنع مشاكل الذاكرة
            if (mb_strlen($message) > 2000) {
                $message = mb_substr($message, 0, 2000) . '...';
            }

            // إزالة الرموز التي قد تسبب مشاكل في JSON
            $message = str_replace(['"', "'", '\\'], ['', '', ''], $message);

            return $message;

        } catch (\Exception $e) {
            // في حالة حدوث خطأ، إرجاع رسالة آمنة
            return 'رسالة تحتوي على رموز غير مدعومة';
        }
    }
}
