<?php

namespace App\Services\AIServices;

class OpenAIService extends BaseAIService
{
    public function __construct()
    {
        parent::__construct('OpenAI');
    }

    protected function getDefaultHeaders(): array
    {
        return [
            'Authorization' => 'Bearer ' . $this->apiKey,
            'Content-Type' => 'application/json',
        ];
    }

    public function chat(string $message, $isResponder = false, ?array $options = []): array
    {
        if (!isset($options['model'])) {
            throw new \InvalidArgumentException('Model is required. Please specify a model name.');
        }

        $messages = [];

       // Get system message from model database and add it to the system message if provided
        $systemMessage = $this->getModelSystemMessage($options['model'], 'chat');
        if (isset($options['system_message'])) {
            $systemMessage = $systemMessage . "\n" . $options['system_message'];
        }
        if ($systemMessage) {
            $messages[] = [
                'role' => 'system',
                'content' => $systemMessage
            ];
        }

        // Add conversation history if provided - Only for responder models
        if ($isResponder && isset($options['conversation_history']) && is_array($options['conversation_history'])) {
            $messages = array_merge($messages, $options['conversation_history']);
        }

        // Add user message
        $messages[] = [
            'role' => 'user',
            'content' => $message
        ];

        $data = [
            'model' => $options['model'],
            'messages' => $messages,
            'max_tokens' => $options['max_tokens']??$this->getModelMaxTokens($options['model'], 'chat') ?? 1000,
            'temperature' => $options['temperature']??$this->getModelTemperature($options['model'], 'chat') ?? 0.7,
        ];

        // Handle vision capabilities
        if (isset($options['images']) && is_array($options['images'])) {
            $data['messages'][count($data['messages']) - 1]['content'] = [
                [
                    'type' => 'text',
                    'text' => $message
                ]
            ];

            foreach ($options['images'] as $image) {
                $data['messages'][count($data['messages']) - 1]['content'][] = [
                    'type' => 'image_url',
                    'image_url' => [
                        'url' => $image
                    ]
                ];
            }
        }

        $response = $this->makeRequest('/v1/chat/completions', $data);

        // Parse and validate OpenAI response
        return $this->parseOpenAIResponse($response);
    }

    /**
     * Parse OpenAI response and extract text content
     */
    private function parseOpenAIResponse(array $response): array
    {
        // Check for successful response with content
        if (isset($response['choices'][0]['message']['content'])) {
            return [
                'success' => true,
                'content' => $response['choices'][0]['message']['content'],
                'raw_response' => $response
            ];
        }

        // Check for errors in response
        if (isset($response['error'])) {
            return [
                'success' => false,
                'error' => $response['error']['type'] ?? 'unknown',
                'message' => $response['error']['message'] ?? 'حدث خطأ غير متوقع.',
                'raw_response' => $response
            ];
        }

        // No valid response found
        return [
            'success' => false,
            'error' => 'INVALID_RESPONSE',
            'message' => 'عذراً، لم أتمكن من فهم رسالتك. يرجى المحاولة مرة أخرى.',
            'raw_response' => $response
        ];
    }

    public function generateImage(string $prompt, ?string $model = null, array $options = []): array
    {
        if (!$model) {
            throw new \InvalidArgumentException('Model is required. Please specify a model name.');
        }

        // Get dynamic options based on model capabilities
        $dynamicOptions = $this->getDefaultImageOptions($model, $options);

        $data = [
            'model' => $model,
            'prompt' => $prompt,
            'n' => $options['n'] ?? 1,
            'response_format' => $options['response_format'] ?? 'url',
        ];

        // Add size if available
        if (isset($dynamicOptions['size'])) {
            $data['size'] = $dynamicOptions['size'];
        }

        // Add quality if available
        if (isset($dynamicOptions['quality'])) {
            $data['quality'] = $dynamicOptions['quality'];
        }

        return $this->makeRequest('/v1/images/generations', $data);
    }

    public function editImage(string $imageUrl, string $prompt, ?string $model = null, array $options = []): array
    {
        if (!$model) {
            throw new \InvalidArgumentException('Model is required. Please specify a model name.');
        }

        // Get dynamic options based on model capabilities
        $dynamicOptions = $this->getDefaultImageOptions($model, $options);

        $data = [
            'image' => $imageUrl, // This would need to be a file upload in real implementation
            'prompt' => $prompt,
            'n' => $options['n'] ?? 1,
            'response_format' => $options['response_format'] ?? 'url',
        ];

        // Add size if available
        if (isset($dynamicOptions['size'])) {
            $data['size'] = $dynamicOptions['size'];
        }

        // Add mask if provided for inpainting
        if (isset($options['mask'])) {
            $data['mask'] = $options['mask'];
        }

        return $this->makeRequest('/v1/images/edits', $data);
    }

    public function generateVideo(string $prompt, ?string $model = null, array $options = []): array
    {
        // OpenAI doesn't have video generation yet, but we implement for future compatibility
        throw new \Exception('Video generation not yet available for OpenAI');
    }

    public function createEmbedding(string $text, string $model = 'text-embedding-ada-002'): array
    {
        $data = [
            'model' => $model,
            'input' => $text,
        ];

        return $this->makeRequest('/v1/embeddings', $data);
    }

    public function moderateContent(string $input): array
    {
        $data = [
            'input' => $input,
        ];

        return $this->makeRequest('/v1/moderations', $data);
    }

    public function transcribeAudio(string $audioFilePath, string $model = 'whisper-1', array $options = []): array
    {
        // Note: This would require multipart/form-data handling
        // Implementation would depend on your specific audio handling needs
        throw new \Exception('Audio transcription not implemented yet');
    }
}
