<?php

namespace App\Services\AIServices;

class ImageRouterService extends BaseAIService
{
    public function __construct()
    {
        parent::__construct('ImageRouter');
    }

    protected function getDefaultHeaders(): array
    {
        return [
            'Authorization' => 'Bearer ' . $this->apiKey,
            'Content-Type' => 'application/json',
        ];
    }

    public function chat(string $message, $isResponder = false, ?array $options = []): array
    {
        // ImageRouter is focused on image generation, not chat
        throw new \Exception('Chat functionality not available for ImageRouter');
    }

    public function generateImage(string $prompt, ?string $model = null, array $options = []): array
    {
        if (!$model) {
            throw new \InvalidArgumentException('Model is required. Please specify a model name.');
        }

        // Get dynamic options based on model capabilities
        $dynamicOptions = $this->getDefaultImageOptions($model, $options);

        $data = [
            'model' => $model,
            'prompt' => $prompt,
            'n' => $options['n'] ?? 1,
            'response_format' => $options['response_format'] ?? 'url',
        ];

        // ImageRouter uses quality levels instead of sizes
        if (isset($dynamicOptions['quality'])) {
            $data['quality'] = $dynamicOptions['quality'];
        }

        // Add additional ImageRouter specific parameters
        if (isset($options['width'])) {
            $data['width'] = $options['width'];
        }

        if (isset($options['height'])) {
            $data['height'] = $options['height'];
        }

        if (isset($options['steps'])) {
            $data['steps'] = $options['steps'];
        }

        if (isset($options['guidance_scale'])) {
            $data['guidance_scale'] = $options['guidance_scale'];
        }

        return $this->makeRequest('/images/generations', $data);
    }

    public function editImage(string $imageUrl, string $prompt, ?string $model = null, array $options = []): array
    {
        // ImageRouter may support image editing in the future
        throw new \Exception('Image editing not yet available for ImageRouter');
    }

    public function generateVideo(string $prompt, ?string $model = null, array $options = []): array
    {
        // ImageRouter is focused on images, not video
        throw new \Exception('Video generation not available for ImageRouter');
    }

    public function getModels(): array
    {
        return $this->makeRequest('/models', []);
    }

    public function getBalance(): array
    {
        return $this->makeRequest('/account/balance', []);
    }

    public function getUsage(array $options = []): array
    {
        $params = [];

        if (isset($options['start_date'])) {
            $params['start_date'] = $options['start_date'];
        }

        if (isset($options['end_date'])) {
            $params['end_date'] = $options['end_date'];
        }

        $queryString = !empty($params) ? '?' . http_build_query($params) : '';

        return $this->makeRequest('/usage' . $queryString, []);
    }

    public function validateApiKey(): array
    {
        return $this->makeRequest('/models', []);
    }

    /**
     * Get available image models with their quality options
     */
    public function getImageModelsWithQualities(): array
    {
        $models = $this->getImageGeneratesModels();
        $result = [];

        foreach ($models as $model) {
            $result[] = [
                'name' => $model->name,
                'qualities' => $model->img_details ?? ['auto'],
                'capabilities' => $model->capabilities,
            ];
        }

        return $result;
    }
}
