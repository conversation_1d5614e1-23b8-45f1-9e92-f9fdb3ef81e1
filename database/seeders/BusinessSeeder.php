<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Business;
use App\Models\Clinic;
use App\Models\Doctor;
use App\Models\Customer;
use App\Models\Appointment;
use App\Models\ClinicOffer;
use App\Models\ClinicService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;

class BusinessSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create the business owner user
        $businessOwner = User::create(
            [
                'name' => 'مجمع المختار الطبي',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin'),
            ]
        );

        // Create the main business
        $business = Business::create([
            'user_id' => $businessOwner->id,
            'name' => 'مجمع المختار الطبي',
            'description' => 'مجمع طبي متكامل يقدم خدمات طبية شاملة بأحدث التقنيات والمعدات الطبية المتطورة',
            'website' => 'https://www.almokhtarmedical.sa',
            'email' => '<EMAIL>',
            'phone' => '+966-12-7234567',
            'address' => 'شارع الملك فهد، حي السلامة',
            'city' => 'الطائف',
            'state' => 'مكة المكرمة',
            'country' => 'المملكة العربية السعودية',
            'logo' => 'logos/almokhtar-medical.png',
            'google_map_link' => 'https://maps.google.com/?q=الطائف+مجمع+المختار+الطبي',
            'google_review_link' => 'https://g.page/r/almokhtarmedical/review',
            'facebook_page_link' => 'https://facebook.com/almokhtarmedical',
            'instagram_page_link' => 'https://instagram.com/almokhtarmedical',
            'twitter_page_link' => 'https://twitter.com/almokhtarmedical',
            'linkedin_page_link' => 'https://linkedin.com/company/almokhtarmedical',
            'youtube_channel_link' => 'https://youtube.com/@almokhtarmedical',
            'snapchat_page_link' => 'https://snapchat.com/add/almokhtarmedical',
            'tiktok_page_link' => 'https://tiktok.com/@almokhtarmedical',
            'whatsapp_number' => '+966-50-1234567',
            'telegram_username' => '@almokhtarmedical',
            'salla_store_url' => null,
        ]);

        // Create clinics
        $clinics = [
            [
                'name' => 'عيادة الأسنان',
                'floor_number' => 'الطابق الأول',
                'room_number' => '101-105',
                'phone_number' => '+966-12-7234568',
                'opening_time' => Carbon::createFromTime(8, 0),
                'closing_time' => Carbon::createFromTime(22, 0),
            ],
            [
                'name' => 'عيادة الباطنة',
                'floor_number' => 'الطابق الثاني',
                'room_number' => '201-203',
                'phone_number' => '+966-12-7234569',
                'opening_time' => Carbon::createFromTime(7, 0),
                'closing_time' => Carbon::createFromTime(20, 0),
            ],
            [
                'name' => 'عيادة الجلدية',
                'floor_number' => 'الطابق الثاني',
                'room_number' => '204-206',
                'phone_number' => '+966-12-7234570',
                'opening_time' => Carbon::createFromTime(9, 0),
                'closing_time' => Carbon::createFromTime(18, 0),
            ],
            [
                'name' => 'عيادة العظام',
                'floor_number' => 'الطابق الثالث',
                'room_number' => '301-303',
                'phone_number' => '+966-12-7234571',
                'opening_time' => Carbon::createFromTime(8, 30),
                'closing_time' => Carbon::createFromTime(19, 0),
            ],
            [
                'name' => 'عيادة النساء والولادة',
                'floor_number' => 'الطابق الثالث',
                'room_number' => '304-307',
                'phone_number' => '+966-12-7234572',
                'opening_time' => Carbon::createFromTime(8, 0),
                'closing_time' => Carbon::createFromTime(21, 0),
            ],
        ];

        $createdClinics = [];
        foreach ($clinics as $clinicData) {
            $createdClinics[] = Clinic::create(array_merge($clinicData, [
                'business_id' => $business->id,
                'is_open' => true,
                'closing_reason' => null,
            ]));
        }

        // Create doctor users and doctors
        $doctors = [
            [
                'doctor' => [
                    'first_name' => 'أحمد',
                    'last_name' => 'السعيد',
                    'specialization' => 'طب الأسنان العام',
                    'experience' => '8 سنوات',
                    'education' => 'بكالوريوس طب الأسنان - جامعة الملك سعود',
                    'certifications' => ['شهادة في تجميل الأسنان', 'دبلوم في زراعة الأسنان'],
                    'languages' => ['العربية', 'الإنجليزية'],
                    'working_days' => [0, 1, 2, 3, 4], // Sunday to Thursday
                    'available_from' => Carbon::createFromTime(8, 0),
                    'available_to' => Carbon::createFromTime(16, 0),
                    'clinic_index' => 0, // Dental clinic
                ],
            ],
            [
                'doctor' => [
                    'first_name' => 'فاطمة',
                    'last_name' => 'الزهراني',
                    'specialization' => 'طب الباطنة',
                    'experience' => '12 سنة',
                    'education' => 'بكالوريوس الطب والجراحة - جامعة أم القرى',
                    'certifications' => ['زمالة الطب الباطني', 'شهادة في أمراض السكري'],
                    'languages' => ['العربية', 'الإنجليزية', 'الفرنسية'],
                    'working_days' => [0, 1, 2, 3, 4, 5], // Sunday to Friday
                    'available_from' => Carbon::createFromTime(7, 0),
                    'available_to' => Carbon::createFromTime(15, 0),
                    'clinic_index' => 1, // Internal medicine clinic
                ],
            ],
            [
                'doctor' => [
                    'first_name' => 'خالد',
                    'last_name' => 'المطيري',
                    'specialization' => 'الأمراض الجلدية',
                    'experience' => '10 سنوات',
                    'education' => 'بكالوريوس الطب والجراحة - جامعة الملك عبدالعزيز',
                    'certifications' => ['زمالة الأمراض الجلدية', 'شهادة في الليزر التجميلي'],
                    'languages' => ['العربية', 'الإنجليزية'],
                    'working_days' => [0, 1, 2, 3, 4], // Sunday to Thursday
                    'available_from' => Carbon::createFromTime(9, 0),
                    'available_to' => Carbon::createFromTime(17, 0),
                    'clinic_index' => 2, // Dermatology clinic
                ],
            ],
            [
                'doctor' => [
                    'first_name' => 'سارة',
                    'last_name' => 'القحطاني',
                    'specialization' => 'النساء والولادة',
                    'experience' => '15 سنة',
                    'education' => 'بكالوريوس الطب والجراحة - جامعة الملك فيصل',
                    'certifications' => ['زمالة النساء والولادة', 'شهادة في الحمل عالي الخطورة'],
                    'languages' => ['العربية', 'الإنجليزية'],
                    'working_days' => [0, 1, 2, 3, 4, 5], // Sunday to Friday
                    'available_from' => Carbon::createFromTime(8, 0),
                    'available_to' => Carbon::createFromTime(16, 0),
                    'clinic_index' => 4, // Gynecology clinic
                ],
            ],
            [
                'doctor' => [
                    'first_name' => 'عبدالله',
                    'last_name' => 'الغامدي',
                    'specialization' => 'جراحة العظام',
                    'experience' => '18 سنة',
                    'education' => 'بكالوريوس الطب والجراحة - جامعة الملك سعود',
                    'certifications' => ['زمالة جراحة العظام', 'شهادة في جراحة المفاصل'],
                    'languages' => ['العربية', 'الإنجليزية', 'الألمانية'],
                    'working_days' => [0, 1, 2, 3, 4], // Sunday to Thursday
                    'available_from' => Carbon::createFromTime(8, 30),
                    'available_to' => Carbon::createFromTime(16, 30),
                    'clinic_index' => 3, // Orthopedics clinic
                ],
            ],
        ];

        $createdDoctors = [];
        foreach ($doctors as $doctorData) {
            $doctorInfo = $doctorData['doctor'];
            $clinicIndex = $doctorInfo['clinic_index'];
            unset($doctorInfo['clinic_index']); // Remove clinic_index before creating doctor

            $doctor = Doctor::create(array_merge($doctorInfo, [
                'business_id' => $business->id,
                'clinic_id' => $createdClinics[$clinicIndex]->id,
                'is_available' => true,
                'unavailability_reason' => null,
            ]));

            $createdDoctors[] = $doctor;
        }

        // Create clinic services
        $services = [
            // Dental services
            [
                'clinic_index' => 0,
                'name' => 'تنظيف الأسنان',
                'description' => 'تنظيف شامل للأسنان وإزالة الجير والبلاك',
                'price' => 150.00,
                'service_url' => null,
            ],
            [
                'clinic_index' => 0,
                'name' => 'حشوات الأسنان',
                'description' => 'حشوات تجميلية وعلاجية للأسنان المتضررة',
                'price' => 250.00,
                'service_url' => null,
            ],
            [
                'clinic_index' => 0,
                'name' => 'زراعة الأسنان',
                'description' => 'زراعة الأسنان بأحدث التقنيات والمواد الطبية',
                'price' => 3500.00,
                'service_url' => null,
            ],
            [
                'clinic_index' => 0,
                'name' => 'تقويم الأسنان',
                'description' => 'تقويم الأسنان التقليدي والشفاف',
                'price' => 4500.00,
                'service_url' => null,
            ],
            // Internal medicine services
            [
                'clinic_index' => 1,
                'name' => 'فحص شامل',
                'description' => 'فحص طبي شامل وتقييم الحالة الصحية العامة',
                'price' => 300.00,
                'service_url' => null,
            ],
            [
                'clinic_index' => 1,
                'name' => 'علاج السكري',
                'description' => 'متابعة وعلاج مرضى السكري',
                'price' => 200.00,
                'service_url' => null,
            ],
            [
                'clinic_index' => 1,
                'name' => 'علاج ضغط الدم',
                'description' => 'متابعة وعلاج ارتفاع ضغط الدم',
                'price' => 180.00,
                'service_url' => null,
            ],
            // Dermatology services
            [
                'clinic_index' => 2,
                'name' => 'علاج الأكزيما',
                'description' => 'علاج الأكزيما والحساسية الجلدية',
                'price' => 220.00,
                'service_url' => null,
            ],
            [
                'clinic_index' => 2,
                'name' => 'إزالة الشامات',
                'description' => 'إزالة الشامات والزوائد الجلدية بالليزر',
                'price' => 400.00,
                'service_url' => null,
            ],
            [
                'clinic_index' => 2,
                'name' => 'علاج حب الشباب',
                'description' => 'علاج حب الشباب وآثاره',
                'price' => 350.00,
                'service_url' => null,
            ],
            // Orthopedics services
            [
                'clinic_index' => 3,
                'name' => 'علاج آلام المفاصل',
                'description' => 'تشخيص وعلاج آلام المفاصل والعظام',
                'price' => 280.00,
                'service_url' => null,
            ],
            [
                'clinic_index' => 3,
                'name' => 'العلاج الطبيعي',
                'description' => 'جلسات العلاج الطبيعي وإعادة التأهيل',
                'price' => 120.00,
                'service_url' => null,
            ],
            // Gynecology services
            [
                'clinic_index' => 4,
                'name' => 'متابعة الحمل',
                'description' => 'متابعة شاملة للحمل والولادة',
                'price' => 250.00,
                'service_url' => null,
            ],
            [
                'clinic_index' => 4,
                'name' => 'فحص النساء الدوري',
                'description' => 'فحص دوري شامل لصحة المرأة',
                'price' => 200.00,
                'service_url' => null,
            ],
        ];

        foreach ($services as $serviceData) {
            ClinicService::create([
                'clinic_id' => $createdClinics[$serviceData['clinic_index']]->id,
                'name' => $serviceData['name'],
                'description' => $serviceData['description'],
                'price' => $serviceData['price'],
                'service_url' => $serviceData['service_url'],
            ]);
        }

        // Create offers
        $offers = [
            [
                'clinic_index' => 0,
                'title' => 'عرض تنظيف الأسنان',
                'description' => 'تنظيف شامل للأسنان مع فحص مجاني - خصم 30%',
                'offer_url' => 'offers/dental-cleaning-offer.jpg',
                'start_date' => Carbon::now()->subDays(10),
                'end_date' => Carbon::now()->addDays(20),
            ],
            [
                'clinic_index' => 0,
                'title' => 'عرض زراعة الأسنان',
                'description' => 'زراعة الأسنان بأفضل الأسعار - خصم يصل إلى 25%',
                'offer_url' => 'offers/dental-implant-offer.jpg',
                'start_date' => Carbon::now()->subDays(5),
                'end_date' => Carbon::now()->addDays(30),
            ],
            [
                'clinic_index' => 1,
                'title' => 'فحص شامل مخفض',
                'description' => 'فحص طبي شامل شامل التحاليل - خصم 40%',
                'offer_url' => 'offers/comprehensive-checkup-offer.jpg',
                'start_date' => Carbon::now()->subDays(3),
                'end_date' => Carbon::now()->addDays(15),
            ],
            [
                'clinic_index' => 2,
                'title' => 'عرض علاج البشرة',
                'description' => 'جلسات علاج البشرة والتجميل - خصم 20%',
                'offer_url' => 'offers/skin-treatment-offer.jpg',
                'start_date' => Carbon::now()->subDays(7),
                'end_date' => Carbon::now()->addDays(25),
            ],
            [
                'clinic_index' => 4,
                'title' => 'عرض متابعة الحمل',
                'description' => 'باقة متابعة الحمل الشاملة - خصم 15%',
                'offer_url' => 'offers/pregnancy-care-offer.jpg',
                'start_date' => Carbon::now()->subDays(12),
                'end_date' => Carbon::now()->addDays(40),
            ],
        ];

        foreach ($offers as $offerData) {
            ClinicOffer::create([
                'clinic_id' => $createdClinics[$offerData['clinic_index']]->id,
                'title' => $offerData['title'],
                'description' => $offerData['description'],
                'offer_url' => $offerData['offer_url'],
                'start_date' => $offerData['start_date'],
                'end_date' => $offerData['end_date'],
            ]);
        }

        // Create customer users and customers
        $customers = [
            [
                'customer' => [
                    'first_name' => 'محمد',
                    'last_name' => 'العتيبي',
                    'phone_number' => '+966-50-1111111',
                    'id_number' => '1234567890',
                    'address' => 'حي الشفا، الطائف',
                    'birth_date' => '1985-03-15',
                    'gender' => 'ذكر',
                    'marital_status' => 'متزوج',
                    'job_title' => 'مهندس',
                    'nationality' => 'سعودي',
                    'religion' => 'مسلم',
                    'education' => 'بكالوريوس',
                    'family_size' => 4,
                    'is_loyal_customer' => true,
                ],
            ],
            [
                'customer' => [
                    'first_name' => 'فاطمة',
                    'last_name' => 'الحربي',
                    'phone_number' => '+966-50-2222222',
                    'id_number' => '2234567891',
                    'address' => 'حي السلامة، الطائف',
                    'birth_date' => '1990-07-22',
                    'gender' => 'أنثى',
                    'marital_status' => 'متزوجة',
                    'job_title' => 'معلمة',
                    'nationality' => 'سعودية',
                    'religion' => 'مسلمة',
                    'education' => 'بكالوريوس',
                    'family_size' => 3,
                    'is_loyal_customer' => true,
                ],
            ],
            [
                'customer' => [
                    'first_name' => 'عبدالرحمن',
                    'last_name' => 'الغامدي',
                    'phone_number' => '+966-50-3333333',
                    'id_number' => '3234567892',
                    'address' => 'حي الحوية، الطائف',
                    'birth_date' => '1988-12-10',
                    'gender' => 'ذكر',
                    'marital_status' => 'أعزب',
                    'job_title' => 'طبيب',
                    'nationality' => 'سعودي',
                    'religion' => 'مسلم',
                    'education' => 'دكتوراه',
                    'family_size' => 1,
                    'is_loyal_customer' => false,
                ],
            ],
            [
                'customer' => [
                    'first_name' => 'نورا',
                    'last_name' => 'القحطاني',
                    'phone_number' => '+966-50-4444444',
                    'id_number' => '4234567893',
                    'address' => 'حي الملك فهد، الطائف',
                    'birth_date' => '1992-05-18',
                    'gender' => 'أنثى',
                    'marital_status' => 'متزوجة',
                    'job_title' => 'محاسبة',
                    'nationality' => 'سعودية',
                    'religion' => 'مسلمة',
                    'education' => 'بكالوريوس',
                    'family_size' => 2,
                    'is_loyal_customer' => true,
                ],
            ],
            [
                'customer' => [
                    'first_name' => 'سعد',
                    'last_name' => 'الزهراني',
                    'phone_number' => '+966-50-5555555',
                    'id_number' => '5234567894',
                    'address' => 'حي الوشحاء، الطائف',
                    'birth_date' => '1980-11-25',
                    'gender' => 'ذكر',
                    'marital_status' => 'متزوج',
                    'job_title' => 'تاجر',
                    'nationality' => 'سعودي',
                    'religion' => 'مسلم',
                    'education' => 'ثانوي',
                    'family_size' => 6,
                    'is_loyal_customer' => true,
                ],
            ],
        ];

        $createdCustomers = [];
        foreach ($customers as $customerData) {
            $customer = Customer::create(array_merge($customerData['customer'], [
                'business_id' => $business->id,
            ]));

            $createdCustomers[] = $customer;
        }

        // Create appointments
        $appointments = [
            [
                'customer_index' => 0,
                'doctor_index' => 0, // Dental doctor
                'clinic_index' => 0,
                'appointment_date' => Carbon::now()->addDays(2)->setTime(10, 0),
                'reason' => 'تنظيف الأسنان',
                'notes' => 'المريض يعاني من تراكم الجير',
                'status' => 'confirmed',
            ],
            [
                'customer_index' => 1,
                'doctor_index' => 3, // Gynecology doctor
                'clinic_index' => 4,
                'appointment_date' => Carbon::now()->addDays(1)->setTime(14, 0),
                'reason' => 'متابعة الحمل',
                'notes' => 'الشهر السادس من الحمل',
                'status' => 'confirmed',
            ],
            [
                'customer_index' => 2,
                'doctor_index' => 1, // Internal medicine doctor
                'clinic_index' => 1,
                'appointment_date' => Carbon::now()->addDays(3)->setTime(9, 30),
                'reason' => 'فحص دوري',
                'notes' => 'فحص سنوي شامل',
                'status' => 'pending',
            ],
            [
                'customer_index' => 3,
                'doctor_index' => 2, // Dermatology doctor
                'clinic_index' => 2,
                'appointment_date' => Carbon::now()->addDays(5)->setTime(11, 0),
                'reason' => 'علاج حب الشباب',
                'notes' => 'مشكلة مستمرة منذ فترة',
                'status' => 'pending',
            ],
            [
                'customer_index' => 4,
                'doctor_index' => 4, // Orthopedics doctor
                'clinic_index' => 3,
                'appointment_date' => Carbon::now()->addDays(7)->setTime(15, 30),
                'reason' => 'آلام في الركبة',
                'notes' => 'ألم مستمر منذ أسبوعين',
                'status' => 'pending',
            ],
            [
                'customer_index' => 0,
                'doctor_index' => 0, // Dental doctor
                'clinic_index' => 0,
                'appointment_date' => Carbon::now()->subDays(10)->setTime(16, 0),
                'reason' => 'حشو أسنان',
                'notes' => 'تم الانتهاء من العلاج بنجاح',
                'status' => 'completed',
            ],
        ];

        foreach ($appointments as $appointmentData) {
            Appointment::create([
                'business_id' => $business->id,
                'customer_id' => $createdCustomers[$appointmentData['customer_index']]->id,
                'doctor_id' => $createdDoctors[$appointmentData['doctor_index']]->id,
                'clinic_id' => $createdClinics[$appointmentData['clinic_index']]->id,
                'appointment_date' => $appointmentData['appointment_date'],
                'reason' => $appointmentData['reason'],
                'notes' => $appointmentData['notes'],
                'status' => $appointmentData['status'],
            ]);
        }
    }
}
