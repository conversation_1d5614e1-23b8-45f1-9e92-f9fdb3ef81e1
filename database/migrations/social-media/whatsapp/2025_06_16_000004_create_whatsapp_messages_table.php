<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    protected $connection = 'whatsapp';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Set SQLite to use UTF-8 encoding and optimize for Unicode handling
        DB::connection($this->connection)->statement('PRAGMA encoding = "UTF-8"');
        DB::connection($this->connection)->statement('PRAGMA foreign_keys = ON');
        DB::connection($this->connection)->statement('PRAGMA journal_mode = WAL');
        DB::connection($this->connection)->statement('PRAGMA synchronous = NORMAL');

        Schema::connection($this->connection)->create('messages', function (Blueprint $table) {
            $table->id();
            $table->string('message_id')->unique(); // WhatsApp message ID
            $table->foreignId('profile_id')->constrained()->onDelete('cascade'); // Foreign key to profiles
            $table->enum('type', ['text', 'image', 'video', 'audio', 'document', 'location', 'contact', 'buttons']);
            $table->text('content')->nullable(); // Message content - UTF-8 safe
            $table->boolean('is_outgoing')->default(false); // true if sent by us
            $table->enum('status', ['pending', 'sent', 'delivered', 'read', 'failed'])->default('pending');
            $table->boolean('is_deleted')->default(false);
            $table->boolean('is_edited')->default(false);
            $table->boolean('is_replayed')->default(false);
            $table->timestamp('sent_at');
            $table->timestamps();

            $table->index('profile_id');
            $table->index('is_outgoing');
            $table->index('is_read');
            $table->index('sent_at');
            $table->index(['profile_id', 'sent_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection($this->connection)->dropIfExists('messages');
    }
};
