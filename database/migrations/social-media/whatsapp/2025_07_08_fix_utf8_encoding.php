<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    protected $connection = 'whatsapp';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // SQLite doesn't have charset/collation like MySQL, but we can ensure proper UTF-8 handling
        // by setting PRAGMA encoding to UTF-8
        
        try {
            // Set SQLite to use UTF-8 encoding
            DB::connection($this->connection)->statement('PRAGMA encoding = "UTF-8"');
            
            // Enable foreign keys
            DB::connection($this->connection)->statement('PRAGMA foreign_keys = ON');
            
            // Set journal mode for better performance
            DB::connection($this->connection)->statement('PRAGMA journal_mode = WAL');
            
            // Set synchronous mode for better performance while maintaining data integrity
            DB::connection($this->connection)->statement('PRAGMA synchronous = NORMAL');
            
            echo "✅ SQLite UTF-8 encoding and performance settings applied successfully\n";
            
        } catch (\Exception $e) {
            echo "❌ Error setting SQLite encoding: " . $e->getMessage() . "\n";
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // SQLite encoding settings are persistent, no need to reverse
        echo "ℹ️ SQLite encoding settings are persistent and don't need to be reversed\n";
    }
};
